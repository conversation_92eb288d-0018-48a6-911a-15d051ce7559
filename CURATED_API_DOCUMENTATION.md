# Curated Content API Documentation

**Base URL:** `/v1/management/curated`  
**Authentication:** JW<PERSON><PERSON> Required  
**Database:** `test_nepali_app`  
**Last Updated:** June 2025

## Overview

The Curated Content API provides comprehensive access to Nepal-themed educational content organized by themes and content sets. This system manages curated questions and tasks related to Nepali culture, geography, history, festivals, food, language, and other educational topics.

## Database Structure

### Collections
- **`themes`** - Theme categories with icons, colors, and metadata
- **`curated_content_set`** - Content sets containing groups of related questions
- **`curated_content_items`** - Individual questions/tasks with proper references

### Data Model
```json
{
  "themes": {
    "_id": "ObjectId",
    "name": "नेपाली संस्कृति र परम्परा",
    "name_en": "Nepali Culture and Traditions",
    "category": "संस्कृति",
    "category_en": "Culture",
    "icon": "🏛️",
    "color": "#FF6B6B",
    "is_active": true
  },
  "curated_content_set": {
    "_id": "ObjectId",
    "title": "नेपाली संस्कृति र परम्परा - सेट 1",
    "theme_id": "ObjectId",
    "user_id": "ObjectId",
    "tasks": ["ObjectId", "ObjectId"],
    "total_tasks": 6,
    "total_score": 60,
    "difficulty_level": 1
  },
  "curated_content_items": {
    "_id": "ObjectId",
    "task_set_id": "ObjectId",
    "user_id": "ObjectId",
    "type": "single_choice|multiple_choice",
    "question": {
      "type": "single_choice",
      "text": "नेपालको राष्ट्रिय फूल कुन हो?",
      "translated_text": "What is the national flower of Nepal?",
      "options": {"a": "गुलाफ", "b": "रोडोडेन्ड्रन", "c": "सूर्यमुखी"},
      "answer_hint": "Rhododendron"
    },
    "correct_answer": {
      "type": "single_choice",
      "value": "b"
    }
  }
}
```

## API Endpoints

### 1. GET /themes
Get all available themes with filtering and pagination.

**Query Parameters:**
- `page` (integer, default: 1): Page number
- `limit` (integer, default: 50, max: 100): Items per page
- `search` (string, optional): Search in theme names and descriptions
- `category` (string, optional): Filter by category
- `is_active` (boolean, optional): Filter by active status
- `start_date` (string, optional): Start date filter (YYYY-MM-DD)
- `end_date` (string, optional): End date filter (YYYY-MM-DD)
- `sort_order` (string, default: "desc"): Sort order (asc/desc)

**Example Request:**
```bash
curl -X 'GET' \
  'http://localhost:8204/v1/management/curated/themes?page=1&limit=10&category=culture&is_active=true' \
  -H 'Authorization: Bearer <jwt_token>'
```

**Response:**
```json
{
  "data": [
    {
      "_id": "685e2955f928ae494af5e969",
      "name": "नेपाली संस्कृति र परम्परा",
      "name_en": "Nepali Culture and Traditions",
      "category": "संस्कृति",
      "category_en": "Culture",
      "icon": "🏛️",
      "color": "#FF6B6B",
      "description": "नेपाली संस्कृति र परम्पराका बारेमा विस्तृत जानकारी",
      "is_active": true,
      "order": 1,
      "created_at": "2025-06-27T09:51:42.937Z"
    }
  ],
  "meta": {
    "page": 1,
    "limit": 10,
    "total": 30,
    "total_pages": 3
  }
}
```

### 2. GET /themes/{theme_id}
Get all content sets for a specific theme.

**Path Parameters:**
- `theme_id` (string): Theme ObjectId

**Query Parameters:**
- `page` (integer, default: 1): Page number
- `limit` (integer, default: 20, max: 100): Items per page
- `difficulty_level` (integer, 1-3, optional): Filter by difficulty
- `status` (string, optional): Filter by status
- `gentype` (string, optional): Filter by generation type

**Example Request:**
```bash
curl -X 'GET' \
  'http://localhost:8204/v1/management/curated/themes/685e2955f928ae494af5e969?page=1&limit=5&difficulty_level=1' \
  -H 'Authorization: Bearer <jwt_token>'
```

**Response:**
```json
{
  "data": [
    {
      "_id": "685e6d3b15fd9122a46a32b3",
      "title": "नेपाली संस्कृति र परम्परा - सेट 1",
      "title_en": "Nepali Culture and Traditions - Set 1",
      "theme_id": "685e2955f928ae494af5e969",
      "difficulty_level": 1,
      "total_tasks": 6,
      "total_score": 60,
      "tasks": [
        "685e6d3b15fd9122a46a32b4",
        "685e6d3b15fd9122a46a32b5"
      ],
      "status": "active",
      "created_at": "2025-06-27T09:51:42.937Z"
    }
  ],
  "meta": {
    "page": 1,
    "limit": 5,
    "total": 20,
    "total_pages": 4
  }
}
```

### 3. GET /filtered
Get filtered content sets across all themes.

**Query Parameters:**
- `page` (integer, default: 1): Page number
- `limit` (integer, default: 20, max: 100): Items per page
- `theme_id` (string, optional): Filter by specific theme ID
- `difficulty_level` (integer, 1-3, optional): Filter by difficulty
- `status` (string, optional): Filter by status
- `gentype` (string, optional): Filter by generation type
- `start_date` (string, optional): Start date filter (YYYY-MM-DD)
- `end_date` (string, optional): End date filter (YYYY-MM-DD)
- `sort_order` (string, default: "desc"): Sort order

### 4. GET /theme/{theme_id} (Theme Details)
Get detailed information about a specific theme.

**Response:**
```json
{
  "data": {
    "_id": "685e2955f928ae494af5e969",
    "name": "नेपाली संस्कृति र परम्परा",
    "name_en": "Nepali Culture and Traditions",
    "category": "संस्कृति",
    "icon": "🏛️",
    "color": "#FF6B6B",
    "statistics": {
      "total_content_sets": 20,
      "total_questions": 120,
      "difficulty_distribution": {
        "easy": 40,
        "medium": 40,
        "hard": 40
      }
    }
  },
  "meta": {
    "success": true,
    "message": "Theme details retrieved successfully"
  }
}
```

### 5. GET /filter/curated
Get dynamic filter options for the filtered endpoint.

**Response:**
```json
{
  "data": {
    "themes": [
      {
        "id": "685e2955f928ae494af5e969",
        "name": "नेपाली संस्कृति र परम्परा",
        "name_en": "Nepali Culture and Traditions"
      }
    ],
    "status_options": ["active", "pending", "completed"],
    "gentype_options": ["primary", "curated", "follow_up"],
    "difficulty_levels": [
      {"value": 1, "label": "Easy"},
      {"value": 2, "label": "Medium"},
      {"value": 3, "label": "Hard"}
    ]
  }
}
```

### 6. GET /questions/{curated_content_id}
Get all questions for a specific curated content set.

**Path Parameters:**
- `curated_content_id` (string): Curated content set ObjectId

**Response:**
```json
[
  {
    "question": {
      "text": "नेपालको राष्ट्रिय फूल कुन हो?",
      "translated_text": "What is the national flower of Nepal?",
      "type": "single_choice"
    }
  }
]
```

### 7. POST /convert/{curated_content_set_id}
Convert curated content set to individual task set for user.

**Path Parameters:**
- `curated_content_set_id` (string): Curated content set ObjectId

**Response (200 - New task set created):**
```json
{
  "success": true,
  "data": {
    "message": "Task set created successfully",
    "task_set_id": "685e2b19c38b3e9857021cf6",
    "status": "created"
  },
  "meta": {
    "timestamp": null,
    "request_id": null
  }
}
```

**Response (201 - Task set already exists):**
```json
{
  "success": true,
  "data": {
    "message": "Task set already exists for user",
    "task_set_id": "685e2b19c38b3e9857021cf6",
    "status": "exists"
  },
  "meta": {
    "timestamp": null,
    "request_id": null
  }
}
```

**Status Codes:**
- `200`: New task set created successfully
- `201`: Task set already exists for user
- `400`: Invalid curated content set ID
- `404`: Curated content set not found
- `500`: Internal server error

### 8. POST /generate
Generate curated content based on input (Background Task).

**Request Body:**
```json
{
  "content": "Generate questions about Nepali festivals"
}
```

**Response:**
```json
{
  "message": "Content generation started",
  "status": "processing"
}
```

### 9. POST /get_prompts
Get user's saved prompts for content generation.

**Response:**
```json
{
  "data": [
    {
      "_id": "507f1f77bcf86cd799439015",
      "content": "Generate questions about Nepali festivals",
      "user_id": "68391d86b8b0e7ec9ababfbb",
      "task_set_id": "507f1f77bcf86cd799439016",
      "created_at": "2025-06-27T10:00:00Z"
    }
  ]
}
```

## Error Responses

All endpoints return standardized error responses:

```json
{
  "detail": "Error message",
  "status_code": 400
}
```

**Common Error Codes:**
- `400`: Bad Request (Invalid parameters)
- `401`: Unauthorized (Invalid/missing JWT token)
- `404`: Not Found (Resource doesn't exist)
- `500`: Internal Server Error

## Authentication

All endpoints require JWT Bearer token authentication:

```bash
Authorization: Bearer <jwt_token>
```

## Rate Limiting

- Standard endpoints: 100 requests/minute
- Content generation: 10 requests/minute
- Bulk operations: 20 requests/minute

## Implementation Examples

### Frontend Integration

**React/TypeScript Example:**
```typescript
// Fetch themes
const fetchThemes = async (filters: ThemeFilters) => {
  const params = new URLSearchParams({
    page: filters.page.toString(),
    limit: filters.limit.toString(),
    ...(filters.category && { category: filters.category }),
    ...(filters.search && { search: filters.search })
  });

  const response = await fetch(`/v1/management/curated/themes?${params}`, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });

  return response.json();
};

// Convert curated content to task set
const convertToTaskSet = async (curatedSetId: string) => {
  const response = await fetch(`/v1/management/curated/convert/${curatedSetId}`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });

  if (response.status === 201) {
    console.log('Task set already exists');
  }

  return response.json();
};
```

### Backend Integration

**Python/AsyncIO Example:**
```python
import aiohttp
import asyncio

async def get_theme_content_sets(theme_id: str, page: int = 1):
    async with aiohttp.ClientSession() as session:
        headers = {'Authorization': f'Bearer {token}'}
        url = f'/v1/management/curated/themes/{theme_id}'
        params = {'page': page, 'limit': 20}

        async with session.get(url, headers=headers, params=params) as response:
            return await response.json()

# Batch process multiple themes
async def process_all_themes():
    themes = await get_themes()
    tasks = [get_theme_content_sets(theme['_id']) for theme in themes['data']]
    results = await asyncio.gather(*tasks)
    return results
```
